﻿"restore":{"projectUniqueName":"E:\\Development\\soc\\SocServer\\SocWorld\\src\\SocBotServer\\SocBotServer.csproj","projectName":"SocBotServer","projectPath":"E:\\Development\\soc\\SocServer\\SocWorld\\src\\SocBotServer\\SocBotServer.csproj","outputPath":"E:\\Development\\soc\\SocServer\\SocWorld\\src\\SocBotServer\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj":{"projectPath":"E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj"},"E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj":{"projectPath":"E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj"},"E:\\Development\\soc\\SocConst\\SocConst.csproj":{"projectPath":"E:\\Development\\soc\\SocConst\\SocConst.csproj"}}}},"warningProperties":{"allWarningsAsErrors":true,"warnAsError":["NU1605"]},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"CommandLineParser":{"target":"Package","version":"[2.9.1, )"},"Microsoft.Extensions.Hosting":{"target":"Package","version":"[9.0.4, )"},"Microsoft.Extensions.Hosting.Abstractions":{"target":"Package","version":"[9.0.4, )"},"MySql.Data":{"target":"Package","version":"[9.3.0, )"},"Newtonsoft.Json":{"target":"Package","version":"[13.0.3, )"},"System.Diagnostics.PerformanceCounter":{"target":"Package","version":"[9.0.4, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}