using SimpleJSON;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.Share.Telemetry;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Test.TestCase
{
    /// <summary>
    /// 该测试为重构时添加，需要进一步完善
    /// </summary>
    internal class PlayerInventoryTest : TestCaseBase
    {
        PlayerEntity player;

        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            AddEntityOnlyInitComponents(player);
            TestBasicAddRequire();
            TestLoadEquipToHorseAndTask();
        }

        private void TestBasicAddRequire()
        {
            var tbl = McCommon.Tables.TbItemConfig.GetOrDefault(12030001);
            TelemetryBase.SetPropValue(tbl, new string[] { "Stack" }, new string[] { "5000" }); // 先调整成一堆5000
            Print("TestBasicAddRequire");
            Print(player.Root.MergeNode(12030001, 2000).ToString()); // 一堆
            Print("2");
            Print(player.Root.MergeNode(12030001, 5000).ToString()); // 变两堆
            Print("3");
            Print(player.Root.RequireNode(12030001, 1000).ToString()); // 消耗第一堆的
            Print("4");
            using (var transaction = EntityTransaction.Start("TestBasicAddRequire"))
            {
                var ret = player.Root.RequireNode(12030001, 7000);
                if (ret != EOpCode.Success)
                {
                    transaction.Rollback("Not enough", false); // 不够，消耗失败
                }
                else
                {
                    Print("Error"); // 不够，消耗失败
                }
            }
            Print(player.Root.RequireNode(12030001, 6000).ToString()); // 消耗完，无残留
            Print("TestBasicAddRequire End");
        }

        private void TestLoadEquipToHorseAndTask()
        {
            Print("TestLoadEquipToHorseAndTask");
            var horse = new HorseEntity();
            horse.AddComponent(new RootNodeComponent());
            horse.AddComponent(new HorseComponent());
            horse.TemplateId = 22030010;
            AddEntityOnlyInitComponents(horse);

            var loadEquipTask = @"
{
  ""id"": 88888888,
  ""type"": 88,
  ""taskPhaseDescribe"": {
    ""index"": 135278640,
    ""text"": """"
  },
  ""taskDescribe"": {
    ""index"": 1456102771,
    ""text"": ""单场对战中，给马匹装备任意4件装备""
  },
  ""taskLongDescribe"": {
    ""index"": 684882152,
    ""text"": """"
  },
  ""subTasks"": [],
  ""counterShowType"": 1,
  ""taskPhaseEndCondition"": 70,
  ""endConditionParameter"": [
    4,
    22030010,
    990619,
  ],
  ""getsource"": {
    ""index"": 217616773,
    ""text"": """"
  },
  ""npcHUDGuide"": {
    ""index"": 1779411092,
    ""text"": """"
  }
}";
            McCommon.Tables.TbQuestPhase.Update(JSONNode.Parse(loadEquipTask));

            FunctionConst.Task.EnableDynamicSwitch();
            FunctionSwitchComponent.Instance.SetEnable(FunctionConst.Task, false);
            player.AddComponent(new PlayerTaskComponent());
            player.AddComponent(new PlayerLootingComponent());
            player.AddComponent(new PlayerVehicleComponent());
            var tc = new Common.CustomType.TaskContainer(88);
            player.ComponentTask.AddTaskContainer(tc);
            tc.PostInit(false);
            using var ctx = NodeOpContext.GetNew("test get task 88888888");
            Print(player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(88888888), ctx).ToString());

            Print("GetHorseEquip");
            var item = NodeSystemHelper.CreateItem(13030006, 1);
            Print(player.Root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventory).WithDetail(new ExistingNode(item))).ToString());
            Print("GetHorseEquipDone");
            var equipConfig = McCommon.Tables.TbHorseEquipment.GetOrDefault(13030006);
            player.ComponentLooting.StartLooting(horse.EntityId);
            Print(player.ComponentLooting.LootingEntityId.ToString());
            Print("LoadEquipToHorseBegin");
            player.ComponentVehicle.LoadEquipToHorse(horse.EntityId, item.Id, equipConfig.BelongPosition[0]);
            Print("LoadEquipToHorseDone");
            FlipTimewheel(1);
            Print((player.Root.GetNodeByPath(NodeSystemType.TaskSystem, 88) as DirectoryNode).ToTreeString(0));
        }
    }
}
