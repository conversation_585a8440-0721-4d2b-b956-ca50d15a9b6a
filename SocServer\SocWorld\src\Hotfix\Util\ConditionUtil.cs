using System.Collections.Generic;
using System.Linq;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Data.task;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld.ClassImpl.Task;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.NodeSystem.VirtualNode;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocWorld
{
    public static class ConditionUtil
    {
        private static readonly Dictionary<TargetData, ConditionBase> conditions = new()
        {
            { TargetData.EquipNum, new EquipNum() },
            { TargetData.OwnNum, new OwnNum() },
            { TargetData.CreateNum, new CreateNum() },
            { TargetData.BuildNum, new BuildNum() },
            { TargetData.ContainerPropNum, new ContainerPropNum() },
            { TargetData.GetAreaNum, new GetAreaNum() },
            { TargetData.HandNum, new HandNum() },
            { TargetData.HuntNum, new HuntNum() },
            { TargetData.UnlockTech, new UnlockTech() },
            { TargetData.BuildLevelNum, new BuildLevelNum() },
            { TargetData.UseItemNum, new UseItemNum() },
            { TargetData.CurrencyNum, new CurrencyNum() },
            { TargetData.MonumentNum, new MonumentNum() },
            { TargetData.TaskCompletedNum, new TaskCompletedNum() },
            { TargetData.OwnNumForType, new OwnNumForType() },
            { TargetData.Drive, new Drive() },
            { TargetData.OpenCardRoomDoor, new OpenCardRoomDoor() },
            { TargetData.OpenTreasureChest, new OpenTreasureChest() },
            { TargetData.KillNum, new KillNum() },
            { TargetData.DamageNum, new DamageNum() },
            { TargetData.OpenTreasureChestType, new OpenTreasureChestType() },
            { TargetData.FinishEvent, new FinishEventNum() },
            { TargetData.UnlockBlueprint, new UnlockBlueprint() },
            { TargetData.CallIntelligenceBonus, new CallIntelligenceBonus() },
            { TargetData.ConsumeItem, new ConsumeItem() },
            { TargetData.ConsumeItemForType, new ConsumeItemForType() },
            { TargetData.ConsumeAny, new ConsumeAny() },
            { TargetData.GetItem, new GetItem() },
            { TargetData.GetItemForType, new GetItemForType() },
            { TargetData.GetItemAny, new GetItemAny() },
            { TargetData.ClientTrigger, new ClientTrigger() },
            { TargetData.UnityDsTrigger, new UnityDsTrigger() },
            { TargetData.DestroyPart, new DestroyPart() },
            { TargetData.DestroyUniquePart, new DestroyUniquePart() },
            { TargetData.CollectNum, new CollectNum() },
            { TargetData.TaskFinish, new TaskFinish() },
            { TargetData.PatrolTeamKillNum, new PatrolTeamKillNum() },
            { TargetData.NPCTeamKillNum, new NPCTeamKillNum() },
            { TargetData.VehicleTeamKillNum, new VehicleTeamKillNum() },
            { TargetData.VisitShop, new VisitShop() },
            { TargetData.StarEngine, new StartEngine() },
            { TargetData.PerfectDelivery, new PerfectDelivery() },
            { TargetData.DisassembleItem, new DisassembleItem() },
            { TargetData.DisassembleAnyItem, new DisassembleAnyItem() },
            { TargetData.OpenPlayerDeathDropBox, new OpenPlayerDeathDropBox() },
            { TargetData.CompleteMiniGame, new CompleteMiniGame() },
            { TargetData.OpenSpawnRuleTreasureChest, new OpenSpawnRuleTreasureChest() },
            { TargetData.EnterAreaBox, new EnterAreaBox() },
            { TargetData.Unload, new Unload() },
            { TargetData.RepairItem, new RepairItem() },
            { TargetData.TeamUp, new TeamUp() },
            { TargetData.GrowCrops, new GrowCrops() },
            { TargetData.Injured, new Injured() },
            { TargetData.Death, new Death() },
            { TargetData.ElectricConnection, new ElectricConnection() },
            { TargetData.BaseProtectionNumLimit, new BaseProtectionNumLimit() },
            { TargetData.AreaProtectionNumLimit, new AreaProtectionNumLimit() },
            { TargetData.CallMonumentElevator, new CallMonumentElevator() },
            { TargetData.Gesture, new Gesture() },
            { TargetData.TeamInvite, new TeamInvite() },
            { TargetData.PossessingTechnology, new PossessingTechnology() },
            { TargetData.EquipWeapon, new EquipWeapon() },
            { TargetData.CompleteGame, new CompleteGame() },
            { TargetData.WinGame, new WinGame() },
            { TargetData.ActiveSafetyBox, new ActiveSafetyBox() },
            { TargetData.RepairDebris, new RepairDebris() },
            { TargetData.OccupyTerritory, new OccupyTerritory() },
            { TargetData.BuildNumAtOtherTerritory, new BuildNumAtOtherTerritory() },
            { TargetData.HydrationNum, new HydrationNum() },
            { TargetData.CaloriesNum, new CaloriesNum() },
            { TargetData.InfoConvertedNum, new InfoConvertedNum() },
            { TargetData.KillUniqueNum, new KillUniqueNum() },
            { TargetData.TakeVehicle, new TakeVehicle() },
            { TargetData.DestroyTerritoryCabinet, new DestroyTerritoryCabinet() },
            { TargetData.PurchaseGoods, new PurchaseGoods() },
            { TargetData.PlacePropsIntoContainer, new PlacePropsIntoContainer() },
            { TargetData.SleepingBagNum, new SleepingBagNum() },
            { TargetData.ComfortNum, new ComfortNum() },
            { TargetData.SaveBlueprintNum, new SaveBlueprintNum() },
            { TargetData.ConstructUseBlueprint, new ConstructUseBlueprint() },
        };

        private static readonly Dictionary<TaskGetType, List<string>> getType2Marks = new()
        {
            { TaskGetType.Make, new List<string>{ OpContextConst.COMMON_COMPOSE } },
            { TaskGetType.Trade, new List<string>{ OpContextConst.VENDING_MACHINE_BUY, OpContextConst.SHOP } },
            { TaskGetType.GetBody, new List<string>{ OpContextConst.GATHER_CORPSE } },
            { TaskGetType.Cook, new List<string>{ OpContextConst.LOOTING_OVEN } },
            { TaskGetType.Plant, new List<string>{ OpContextConst.LOOTING_PLANT_BOX } },
            { TaskGetType.SupplyBox, new List<string>{ OpContextConst.LOOTING_SPAWN_BOX_OR_AIRDROP_BOX, OpContextConst.LOOTING } },
            { TaskGetType.MiningMachine, new List<string>{ OpContextConst.LOOTING_MINING_DIG } },
            { TaskGetType.SulfurMine, new List<string>{ OpContextConst.LOOTING_SULFUR_DIG } },
            { TaskGetType.Fuel, new List<string>{ OpContextConst.LOOTING_FUEL_DIG } },
            { TaskGetType.Gather, new List<string>{ OpContextConst.GATHER, OpContextConst.PICK_UP_RESOURCE } },
            { TaskGetType.Decomposer, new List<string>{ OpContextConst.LOOTING_DECOMPOSER } },
            { TaskGetType.Composter, new List<string>{ OpContextConst.LOOTING_COMPOSTER } },
            { TaskGetType.OilBarrel, new List<string>{ OpContextConst.PICK_UP_FROM_OIL_BARREL } },
            { TaskGetType.Quarry, new List<string>{ OpContextConst.LOOTING_QUARRY_DIG } },
            { TaskGetType.Oven, new List<string>{ OpContextConst.LOOTING_OVEN } },
            { TaskGetType.Pickup, new List<string>{ OpContextConst.PICK_UP } },
            { TaskGetType.ResearchBench, new List<string>{ OpContextConst.LOOTING_RESEARCH_BENCH } },
            { TaskGetType.MonsterCorpseBox, new List<string>{ OpContextConst.LOOTING_MONSTER_DROP_REWARD_BOX } },
            { TaskGetType.PlayerCorpseBox, new List<string>{ OpContextConst.LOOTING_PLAYER_DROP_REWARD_BOX } },
            { TaskGetType.BuildingDestroyBox, new List<string>{ OpContextConst.LOOTING_BUILDING_DROP_REWARD_BOX } },
            { TaskGetType.MixingTable, new List<string>{ OpContextConst.MIXING } },
        };

        private static readonly Dictionary<TaskUseType, List<string>> useType2Marks = new()
        {
            { TaskUseType.Make, new List<string>{ OpContextConst.COMMON_COMPOSE_DONE } },
            { TaskUseType.Trade, new List<string>{ OpContextConst.SHOP, OpContextConst.VENDING_MACHINE_BUY } },
            { TaskUseType.Build, new List<string>{ OpContextConst.CONSTRUCTION, OpContextConst.CONSTRUCTION_BY_UID, OpContextConst.CONSTRUCTION_SET, OpContextConst.CONSTRUCTION_RECOVER } },
            { TaskUseType.TechTree, new List<string>{ OpContextConst.TECHNOLOGY_UNLOCK } },
            { TaskUseType.Use, new List<string>{ OpContextConst.USE_ITEM } },
            { TaskUseType.Throw, new List<string>{ OpContextConst.THROW_WEAPON_OR_GRENADE } },
            { TaskUseType.Submit, new List<string>{ OpContextConst.SUBMIT_ITEM } },
            { TaskUseType.PlantingBox, new List<string>{ OpContextConst.LOOTING_PLANT_BOX } },
        };

        private static Dictionary<string, List<TaskGetType>> mark2GetTypes = new();
        private static Dictionary<string, List<TaskUseType>> mark2UseTypes = new();

        public static void Init()
        {
            InitGetAndUseTypeMapping();
            Register();
        }

        private static void Register()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerEquipChange>((self, evt) => { self.ProcessEvent(EventTypeId<PlayerEquipChange>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ItemAdd>((self, evt) =>
            {
                if (evt.HasEnteredInventoryBefore)
                {
                    // 之前进过背包，计数不反复刷，就创建一个数值为0的事件
                    evt = new ItemAdd(evt.BizId, 0, evt.Mark, evt.HasEnteredInventoryBefore);
                }

                self.ProcessEvent(EventTypeId<ItemAdd>.EventId, evt);

                var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(evt.BizId);
                if (itemConfig == null) return;
                var manufacturingId = itemConfig.Manufacturing;
                if (manufacturingId == 0) return;
                self.ProcessEvent(ConditionInnerClassId.ITEM_MANUFACTURING_ADD_ID, new ItemAddByManufacturing(itemConfig.Manufacturing, evt.IncrementCount));
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ItemRemove>((self, evt) =>
            {
                self.ProcessEvent(EventTypeId<ItemRemove>.EventId, evt);
                var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(evt.BizId);
                if (itemConfig == null) return;
                var manufacturingId = itemConfig.Manufacturing;
                if (manufacturingId == 0) return;
                self.ProcessEvent(ConditionInnerClassId.ITEM_MANUFACTURING_REMOVE_ID, new ItemRemoveByManufacturing(itemConfig.Manufacturing, evt.RemoveCount));
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ManufactureItem>((self, evt) => { self.ProcessEvent(EventTypeId<ManufactureItem>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ConstructEvent>((self, evt) =>
            {
                self.ProcessEvent(EventTypeId<ConstructEvent>.EventId, evt);

                var territoryEntity = EntityManager.Instance.GetEntity(evt.TerritoryId) as TerritoryEntity;
                if (null != territoryEntity && !territoryEntity.PermissionComp.HasSpecialPermission(self.RoleId, PermissionGroupConst.BUILD))
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.BUILD_NUM_AT_OTHER_TERRITORY, evt.BizId);
                    self.ProcessEvent(ref info);
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<AreaEvent>((self, evt) => { self.ProcessEvent(EventTypeId<AreaEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<RepairItemEvent>((self, evt) => { self.ProcessEvent(EventTypeId<RepairItemEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<HuntEvent>((self, evt) => { self.ProcessEvent(EventTypeId<HuntEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<UnlockTechEvent>((self, evt) =>
            {
                self.ProcessEvent(EventTypeId<UnlockTechEvent>.EventId, evt);

                var info = new ConditionInnerInfo(ConditionInnerClassId.POSSESSING_TECHNOLOGY, evt.BizId);
                self.ProcessEvent(ref info);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ShareTechnologyEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.POSSESSING_TECHNOLOGY, -1);
                self.ProcessEvent(ref info);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<UseItemEvent>((self, evt) => { self.ProcessEvent(EventTypeId<UseItemEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<TaskTypeFinishInfo>((self, evt) => { self.ProcessEvent(EventTypeId<TaskTypeFinishInfo>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OpenCardRoomEvent>((self, evt) => { self.ProcessEvent(EventTypeId<OpenCardRoomEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<DriveEvent>((self, evt) => { self.ProcessEvent(EventTypeId<DriveEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<KillEvent>((self, evt) => 
            {
                HashSet<int> processed = new();
                foreach (var selectId in GetDamageConditionId(self.EntityId, evt.Data))
                {
                    if (!processed.Contains(selectId))
                    {
                        var info = new ConditionInnerInfo(ConditionInnerClassId.KILL_EVENT_ID, selectId);
                        self.ProcessEvent(ref info);
                        processed.Add(selectId);
                    }
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<DamageEvent>((self, evt) => { self.ProcessEvent(EventTypeId<DamageEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<FinishActionEvent>((self, evt) => { self.ProcessEvent(EventTypeId<FinishActionEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<UnlockBlueprintEvent>((self, evt) => { self.ProcessEvent(EventTypeId<UnlockBlueprintEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<CallIntelligenceBonusEvent>((self, evt) => { self.ProcessEvent(EventTypeId<CallIntelligenceBonusEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<UnityDsTriggerEvent>((self, evt) => { self.ProcessEvent(EventTypeId<UnityDsTriggerEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<DestroyPartEvent>((self, evt) =>
            {
                self.ProcessEvent(EventTypeId<DestroyPartEvent>.EventId, evt);

                if (evt.BizId == (long)PartType.ToolCupboard)
                {
                    var territory = EntityManager.Instance.GetEntity(evt.TerritoryId) as TerritoryEntity;
                    if (null != territory)
                    {
                        var groupId = 0;
                        if (null != territory.DeadSheepRecord)
                        {
                            groupId = territory.DeadSheepRecord.BuildingGroupId;
                        }

                        var info = new ConditionInnerInfo(ConditionInnerClassId.DESTROY_TERRITORYCABINET, groupId);
                        self.ProcessEvent(ref info);
                    }
                }
            });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<CollectEvent>((self, evt) => { self.ProcessEvent(EventTypeId<CollectEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PoiTeamKillEvent>((self, evt) =>
            {
                if (evt.Type is (PoiTeamKillEvent.EType.Patrol or PoiTeamKillEvent.EType.Boyband))
                {
                    self.ProcessEvent(EventTypeId<PoiTeamKillEvent>.EventId, evt);
                }

                if (evt.Type == PoiTeamKillEvent.EType.Patrol)
                {
                    self.ProcessEvent(ConditionInnerClassId.PATROL_TEAM_KILL_ID, evt);
                }
                else if (evt.Type == PoiTeamKillEvent.EType.Gunship)
                {
                    self.ProcessEvent(ConditionInnerClassId.GUNSHIP_KILL_ID, evt);
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<VisitShopEvent>((self, evt) => { self.ProcessEvent(EventTypeId<VisitShopEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<StartTrainCarEvent>((self, evt) => { self.ProcessEvent(EventTypeId<StartTrainCarEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerStartLootingEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.LOOTING_STORAGE_INFO_ID, evt.TemplateId);
                self.ProcessEvent(ref info, true);

                if (self.ComponentLooting.CurrentLootingEntity is BoxEntity box && box.GetComponent(EComponentIdEnum.Box) is BoxComponent boxComp && !boxComp.LootedPlayerIds.Contains(self.EntityId))
                {
                    info = new ConditionInnerInfo(ConditionInnerClassId.OPEN_TREASURE_CHEST_ID, box.TemplateId);
                    self.ProcessEvent(ref info);

                    var spawnComp = box.GetComponent(EComponentIdEnum.SpawnComponent) as SpawnComponent;
                    if (null != spawnComp && EntityManager.Instance.GetEntity(spawnComp.SpawnLinkId) is SpawnGroupEntity groupEntity)
                    {
                        info = new ConditionInnerInfo(ConditionInnerClassId.OPEN_TREASURE_CHEST_FOR_SPAWN_RULE_ID, groupEntity.GroupId);
                        self.ProcessEvent(ref info);
                    }

                    var boxConfig = McCommon.Tables.TbTreasureBox.GetOrDefault(box.TemplateId);
                    if (boxConfig != null)
                    {
                        info = new ConditionInnerInfo(ConditionInnerClassId.OPEN_TREASURE_CHEST_FOR_TYPE_ID, boxConfig.BoxType);
                        self.ProcessEvent(ref info);
                    }
                }
            });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<LootingStorageChangeEvent>((self, evt) =>
            {
                if (!evt.IsQuickOperate)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.LOOTING_STORAGE_INFO_ID, evt.TemplateId);
                    self.ProcessEvent(ref info, true);
                }

                if (evt.IncrementCount > 0)
                {
                    self.ProcessEvent(ConditionInnerClassId.PLACE_PROPS_INTO_CONTAINER, new PlacePropsIntoContainerInfo(evt.TemplateId, evt.BizId, evt.IncrementCount));
                }
            });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<TrainUnloadEvent>((self, evt) =>
            {
                // 触发卸货事件，通过Count字段传递卸货方式信息
                var info = new ConditionInnerInfo(ConditionInnerClassId.UNLOAD_ID, evt.BizId, evt.UnloadType);
                self.ProcessEvent(ref info);

                // 处理完美送达逻辑
                if (evt.HealthPercent >= McCommon.Tables.TbQuestConst.PerfectDeliveryHPPercent)
                {
                    info = new ConditionInnerInfo(ConditionInnerClassId.PERFECT_DELIVERY_ID, evt.BizId);
                    self.ProcessEvent(ref info);
                }
                else
                {
                    self.Logger.Info("[OnTrainUnloadEvent] is not full health.");
                }
            });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<DisassembleItemEvent>((self, evt) =>
            {
                var info1 = new ConditionInnerInfo(ConditionInnerClassId.DECOMPOSER_ITEM_ID, evt.BizId, evt.Count);
                self.ProcessEvent(ref info1);

                var info2 = new ConditionInnerInfo(ConditionInnerClassId.DECOMPOSER_ANY_ITEM_ID, -1, evt.Count);
                self.ProcessEvent(ref info2);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<TaskFinishEvent>((self, evt) => { self.ProcessEvent(EventTypeId<TaskFinishEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<CompleteMiniGameEvent>((self, evt) => { self.ProcessEvent(EventTypeId<CompleteMiniGameEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<GrowCropsEvent>((self, evt) => { self.ProcessEvent(EventTypeId<GrowCropsEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerLootingBoxEvent>((self, evt) =>
            {
                if (!evt.HasLootedBefore)
                {
                    if (evt.IsCorpseBox)
                    {
                        self.ProcessEvent(EventTypeId<PlayerLootingBoxEvent>.EventId, evt);
                        self.ProcessEvent(EventTypeId<FinishActionEvent>.EventId, new FinishActionEvent(McCommon.Tables.TbQuestConst.PlayerDeathDropBox));
                    }

                    if (EntityManager.Instance.GetEntity(evt.TargetEntityId) is BoxEntity { SrcEntityType: EntityTypeId.PartEntity })
                    {
                        self.ProcessEvent(EventTypeId<FinishActionEvent>.EventId,
                            new FinishActionEvent(McCommon.Tables.TbQuestConst.LootingCorpseBox));
                    }
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<EnterAreaBoxEvent>((self, evt) => { self.ProcessEvent(EventTypeId<EnterAreaBoxEvent>.EventId, evt); });
            EntityStaticCallback<TeamEntity>.AddStaticCallback<JoinTeamWithPlayerEntity>((self, evt) =>
            {
                var playerEntity = evt.JoinPlayer;
                var info = new ConditionInnerInfo(ConditionInnerClassId.TEAM_UP, -1, 1);
                playerEntity.ProcessEvent(ref info);

                var info1 = new ConditionInnerInfo(ConditionInnerClassId.POSSESSING_TECHNOLOGY, -1);
                playerEntity.ProcessEvent(ref info1);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<SelfDieEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.DEATH, -1, 1);
                self.ProcessEvent(ref info);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ElectricConnectionEvent>((self, evt) => { self.ProcessEvent(EventTypeId<ElectricConnectionEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerProtectionChangedEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.BASE_PROTECTOIN_NUM_LIMIT, -1, 1);
                self.ProcessEvent(ref info);

                var info1 = new ConditionInnerInfo(ConditionInnerClassId.AREA_PROTECTOIN_NUM_LIMIT, -1, 1);
                self.ProcessEvent(ref info1);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<CallElevatorEvent>((self, evt) =>
            {
                if (self.MonumentId > 0)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.CALL_MONUMENT_LELEVATOR, -1, 1);
                    self.ProcessEvent(ref info);
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<GestureEvent>((self, evt) => { self.ProcessEvent(EventTypeId<GestureEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<TeamInviteEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.TEAM_INVITE, evt.IsFromGesture ? 1 : 0, 1);
                self.ProcessEvent(ref info);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerCompleteGameEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.COMPLETE_GAME, evt.GameModeId);
                self.ProcessEvent(ref info);

                if (evt.IsWin)
                {
                    var info2 = new ConditionInnerInfo(ConditionInnerClassId.WIN_GAME, evt.GameModeId, 1, new WinGameInfo(evt.GameModeId, 1, evt.CampId));
                    self.ProcessEvent(ref info2);
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<RepairDebrisEvent>((self, evt) => { self.ProcessEvent(EventTypeId<RepairDebrisEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OccupyTerritoryEvent>((self, evt) => { self.ProcessEvent(EventTypeId<OccupyTerritoryEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<FinishConvertReputationEvent>((self, evt) => { self.ProcessEvent(EventTypeId<FinishConvertReputationEvent>.EventId, evt); });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PurchaseGoodsEvent>((self, evt) => { self.ProcessEvent(EventTypeId<PurchaseGoodsEvent>.EventId, evt); });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<SaveBlueprintEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.SAVE_BLUEPRINT, -1, 1);
                self.ProcessEvent(ref info);
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<ConstructUseBlueprintEvent>((self, evt) =>
            {
                var info = new ConditionInnerInfo(ConditionInnerClassId.CONSTRUCT_USE_BLUEPRINT, -1, 1);
                self.ProcessEvent(ref info);
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.CURRENT_WEAPON_ID, (CustomTypeBase obj, long oldValue, long newValue) =>
            {
                var self = obj as PlayerEntity;
                var itemNode = self.Root.GetNodeById(newValue) as BaseItemNode;
                if (itemNode == null)
                {
                    var oldItemNode = self.Root.GetNodeById(oldValue) as BaseItemNode;
                    if (oldItemNode != null)
                    {
                        ProcessEvent(self, ConditionInnerClassId.HAND_ITEM_CHANGE_ID, new HandItemChangeInfo(oldItemNode.BizId));
                    }
                }
                else
                {
                    ProcessEvent(self, ConditionInnerClassId.HAND_ITEM_CHANGE_ID, new HandItemChangeInfo(itemNode.BizId));
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.COMFORT, (CustomTypeBase obj, float _, float newValue) =>
            {
                var self = obj as PlayerEntity;
                var info = new ConditionInnerInfo(ConditionInnerClassId.COMFORT_NUM, -1, 0);
                self.ProcessEvent(ref info);
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.MONUMENT_TYPE_ID, (CustomTypeBase obj, int oldValue, int newValue) =>
            {
                var self = (obj as PlayerEntity);
                if (oldValue != -1)
                {
                    ProcessEvent(self, ConditionInnerClassId.MONUMENT_CHANGE_ID, new MonumentChangeInfo(oldValue, MonumentChangeInfo.EType.Leave));
                }

                if (newValue != -1)
                {
                    ProcessEvent(self, ConditionInnerClassId.MONUMENT_CHANGE_ID, new MonumentChangeInfo(newValue, MonumentChangeInfo.EType.Enter));
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerDamageableComponent.CLASS_HASH, PlayerDamageableComponent.PropertyIds.HP, (CustomTypeBase obj, float oldValue, float newValue) =>
            {
                var comp = obj as PlayerDamageableComponent;
                var self = comp.ParentEntity as PlayerEntity;
                if (oldValue > newValue)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.INJURED, -1, 1);
                    self.ProcessEvent(ref info);
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.SAFETY_BOX_ENTITY_ID, (CustomTypeBase obj, long oldValue, long newValue) =>
            {
                var self = obj as PlayerEntity;
                if (self.SafetyBoxEntityId > 0)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.ACTIVE_SAFETY_BOX, -1, 1);
                    self.ProcessEvent(ref info);
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.MOUNTABLE_ID, (CustomTypeBase obj, long oldValue, long newValue) =>
            {
                var self = obj as PlayerEntity;
                if (self.MountableId <= 0) return;

                long templateId = 0;
                var vehicleEntity = EntityManager.Instance.GetEntity(self.MountableId) as ITemplateEntity;
                if (vehicleEntity == null)
                {
                    var embeddedCustom = EmbeddedCustomManager.Instance.GetEmbedded(self.MountableId);
                    if (null == embeddedCustom) return;

                    var parentEntity = embeddedCustom.ParentRef as ITemplateEntity;
                    if (null == parentEntity) return;

                    templateId = parentEntity.TemplateId;
                }
                else
                {
                    templateId = vehicleEntity.TemplateId;
                }

                var info = new ConditionInnerInfo(ConditionInnerClassId.TAKE_VEHICLE, templateId, 1);
                self.ProcessEvent(ref info);
            });

            DirectoryCallbackHelper.RegisterStaticDirectoryCallback(PlayerEntity.CLASS_HASH, (int)EComponentIdEnum.PlayerInventoryComponentId, NodePathConst.PlayerInventoryBelt, (PlayerInventoryComponent comp, IDirectoryNode parent, NodeBase node, bool isAdd) =>
            {
                if (node is WeaponItemNode weapon)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.EQUIP_WEAPON, -1, 1);
                    comp.Player.ProcessEvent(ref info);
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerConstructionComponent.PropertyIds.SLEEPING_BAG_INFO, (CustomTypeBase obj, long _, long _) =>
            {
                if (obj is PlayerConstructionComponent constructionComp && constructionComp.Player is PlayerEntity player)
                {
                    var info = new ConditionInnerInfo(ConditionInnerClassId.SLEEPING_BAG, -1, 0);
                    player.ProcessEvent(ref info);
                }
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.HYDRATION, (CustomTypeBase obj, float _, float _) =>
            {
                var self = obj as PlayerEntity;
                var info = new ConditionInnerInfo(ConditionInnerClassId.HYDRATION_NUM, -1, 0);
                self.ProcessEvent(ref info);
            });

            PropertyStaticCallback.SubscribePropertyChange(PlayerEntity.CLASS_HASH, PlayerEntity.PropertyIds.CALORIES, (CustomTypeBase obj, float _, float _) =>
            {
                var self = obj as PlayerEntity;
                var info = new ConditionInnerInfo(ConditionInnerClassId.CALORIES_NUM, -1, 0);
                self.ProcessEvent(ref info);
            });
        }

        public static void ProcessEvent(this PlayerEntity self, int eventId, IEventWithBizId eventData)
        {
            var count = 1;
            if (eventData is IEventWithCount iewc)
            {
                count = iewc.Count;
            }
            var info = new ConditionInnerInfo(eventId, eventData.BizId, count, eventData);
            self.ProcessEvent(ref info);
        }

        private static void ProcessEvent(this PlayerEntity player, ref ConditionInnerInfo info, bool isForbiddenTeamShare = false)
        {
            player.ComponentTask.ProcessTaskEvent(player, ref info, isForbiddenTeamShare);
        }

        private static void InitGetAndUseTypeMapping()
        {
            foreach (var (getType, marks) in getType2Marks)
            {
                foreach (var mark in marks)
                {
                    if (mark2GetTypes.TryGetValue(mark, out var types))
                    {
                        types.Add(getType);
                    }
                    else
                    {
                        mark2GetTypes.Add(mark, new List<TaskGetType> { getType });
                    }
                }
            }

            foreach (var (useType, marks) in useType2Marks)
            {
                foreach (var mark in marks)
                {
                    if (mark2UseTypes.TryGetValue(mark, out var types))
                    {
                        types.Add(useType);
                    }
                    else
                    {
                        mark2UseTypes.Add(mark, new List<TaskUseType> { useType });
                    }
                }
            }
        }

        public static ConditionBase GetCondition(TargetData type) => conditions.TryGetValue(type, out var condition) ? condition : null;

        public static IEnumerable<long> GetConditionBizIds(long bizId)
        {
            if (bizId <= NodeConst.IdGroupEnd && bizId >= NodeConst.IdGroupStart)
            {
                var idGroupConfig = McCommon.Tables.TbTaskIdGroup.GetOrDefault(bizId);
                if (null != idGroupConfig)
                {
                    foreach (var id in idGroupConfig.IdList)
                    {
                        yield return id;
                    }
                }
            }
            else
            {
                yield return bizId;
            }
        }

        public static int GetCurProcess(PlayerEntity player, int conditionId, long[] param)
        {
            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null || conditionConfig.CounterChange == CounterChange.Increment)
            {
                return 0;
            }

            var condition = GetCondition(conditionConfig.TargetData);
            if (null == condition)
            {
                return 0;
            }

            return condition.GetValue(player, param);
        }

        private static IEnumerable<TaskGetType> GetItemGetTypes(string mark)
        {
            if (mark == null || !mark2GetTypes.TryGetValue(mark, out var getTypes))
            {
                yield break;
            }

            foreach (var getType in getTypes)
            {
                yield return getType;
            }
        }

        private static IEnumerable<TaskUseType> GetItemUseTypes(string mark)
        {
            if (mark == null || !mark2UseTypes.TryGetValue(mark, out var useTypes))
            {
                yield break;
            }

            foreach (var useType in useTypes)
            {
                yield return useType;
            }
        }

        public static IEnumerable<int> GetDamageConditionId(long playerEntityId, DamageInstance data)
        {
            var playerEntity = EntityManager.Instance.GetPlayerEntity(playerEntityId);
            if (null == playerEntity)
            {
                yield break;
            }

            foreach (var id in playerEntity.ComponentTask.damageConditionIds)
            {
                var config = McCommon.Tables.TbTaskDamageCondition.GetOrDefault(id);
                if (null != config && IsMatchDamageCondition(config, playerEntity, data))
                {
                    yield return id;
                }
            }
        }

        private static bool IsMatchDamageCondition(TaskDamageCondition config, PlayerEntity playerEntity, DamageInstance data)
        {
            // 未知的伤害类型
            if (!EntityTypeId.EntityName2Type.TryGetValue(config.AttackObject, out var entityTypeId)) return false;

            // 实体类型不匹配
            if (entityTypeId != data.TargetEntityType) return false;

            // 伤害关系不匹配
            if (/*config.DamageObject != DamageRelation.None && */data.DamageRelation != (int)config.DamageObject) return false;

            // 目标实体id不匹配
            if (config.TargetId.Length > 0 && !config.TargetId.Contains(data.TargetEntityTableId)) return false;

            // 距离不匹配
            if (config.InsideOrOutside == TaskDistanceType.Inside && config.Distance < data.Distance) return false;
            if (config.InsideOrOutside == TaskDistanceType.Outside && config.Distance > data.Distance) return false;

            // 武器不匹配
            if (config.WeaponId.Length > 0 && !config.WeaponId.Contains(data.WeaponTemplateId)) return false;

            // 武器类型不匹配
            var weaponType = HeldItemUtilityShare.GetEquipType(data.WeaponTemplateId);
            if (config.WeaponTypeId.Length > 0 && !config.WeaponTypeId.Contains((long)weaponType)) return false;

            // 装备配件
            if (config.PartId.Length != 0)
            {
                foreach (var id in config.PartId)
                {
                    var isMatched = false;
                    foreach (var weaponTemplateId in data.WeaponPartsTemplateId)
                    {
                        if (weaponTemplateId == id)
                        {
                            isMatched = true;
                            break;
                        }
                    }
                    if (!isMatched) return false;
                }
            }

            // 不能装备配件（参数为-1代表不能装备任何配件）
            if (config.NopartId.Length != 0)
            {
                foreach (var id in config.NopartId)
                {
                    foreach (var weaponTemplateId in data.WeaponPartsTemplateId)
                    {
                        if (id == -1 || weaponTemplateId == id) return false;
                    }
                }
            }

            // 穿戴防具
            if (config.ArmorId.Length != 0)
            {
                var wearContainer = playerEntity.Root.GetNodeByPath(NodePathConst.PlayerInventoryWear) as ItemContainerNode;
                foreach (var id in config.ArmorId)
                {
                    var isMatched = false;
                    foreach (var (nodeId, node) in wearContainer)
                    {
                        if (node.BizId == id)
                        {
                            isMatched = true;
                            break;
                        }
                    }
                    if (!isMatched) return false;
                }
            }

            // 不能穿戴防（参数为-1代表不能穿戴任何防具）
            if (config.NoarmorId.Length != 0)
            {
                var wearContainer = playerEntity.Root.GetNodeByPath(NodePathConst.PlayerInventoryWear) as ItemContainerNode;
                foreach (var id in config.NoarmorId)
                {
                    foreach (var (nodeId, node) in wearContainer)
                    {
                        if (id == -1 || node.BizId == id) return false;
                    }
                }
            }

            // 所属时间
            if (config.Time != 0)
            {
                if (config.Time == 1 && !ServerInstanceEntity.Instance.IsDaytime) return false;
                if (config.Time == 2 && ServerInstanceEntity.Instance.IsDaytime) return false;
            }

            // 所处地点
            if (config.PlaceId.Length != 0)
            {
                if (playerEntity.MonumentTypeId < 0) return false;
                var isMatched = false;
                foreach (var id in config.PlaceId)
                {
                    if (playerEntity.MonumentTypeId == id)
                    {
                        isMatched = true;
                        break;
                    }
                }

                if (!isMatched) return false;
            }

            // 载具类型
            if (config.VehicleId.Length != 0)
            {
                if (playerEntity.MountableId == 0) return false;

                long templateId = 0;
                var vehicleEntity = EntityManager.Instance.GetEntity(playerEntity.MountableId) as ITemplateEntity;
                if (vehicleEntity == null)
                {
                    var embeddedCustom = EmbeddedCustomManager.Instance.GetEmbedded(playerEntity.MountableId);
                    if (null == embeddedCustom) return false;

                    var parentEntity = embeddedCustom.ParentRef as ITemplateEntity;
                    if (null == parentEntity) return false;

                    templateId = parentEntity.TemplateId;
                }
                else
                {
                    templateId = vehicleEntity.TemplateId;
                }

                var isMatched = false;
                foreach (var id in config.VehicleId)
                {
                    if (templateId == id)
                    {
                        isMatched = true;
                        break;
                    }
                }

                if (!isMatched) return false;
            }

            // 子弹类型匹配
            if (config.BulletId.Length != 0)
            {
                var isMatched = false;
                foreach (var id in config.BulletId)
                {
                    if (id == data.BulletTemplateId)
                    {
                        isMatched = true;
                        break;
                    }
                }
                if (!isMatched) return false;
            }

            return true;
        }

        public static bool Compare(CompareType compareType, int count, int configCount)
        {
            switch (compareType)
            {
                case CompareType.Less:
                    if (count < configCount)
                        return true;
                    break;
                case CompareType.LessOrEqual:
                    if (count <= configCount)
                        return true;
                    break;
                case CompareType.Equal:
                    if (count == configCount)
                        return true;
                    break;
                case CompareType.GreaterOrEqual:
                    if (count >= configCount)
                        return true;
                    break;
                case CompareType.Greater:
                    if (count > configCount)
                        return true;
                    break;
                default:
                    break;
            }

            return false;
        }

        public static bool CheckItemGetType(ref ConditionInnerInfo info, TaskGetType[] targetTypes)
        {
            if (info.OriginEvent is not IEventWithMark iewm) return false;
            if (!mark2GetTypes.ContainsKey(iewm.Mark)) return false;
            if (targetTypes.Contains(TaskGetType.All)) return true;

            foreach (var getType in GetItemGetTypes(iewm.Mark))
            {
                if (targetTypes.Contains(getType))
                {
                    return true;
                }
            }

            return false;
        }

        public static bool CheckItemUseType(ref ConditionInnerInfo info, TaskUseType[] targetTypes)
        {
            if (info.OriginEvent is not IEventWithMark iewm) return false;
            if (!mark2UseTypes.ContainsKey(iewm.Mark)) return false;
            if (targetTypes.Contains(TaskUseType.All)) return true;

            foreach (var useType in GetItemUseTypes(iewm.Mark))
            {
                if (targetTypes.Contains(useType))
                {
                    return true;
                }
            }

            return false;
        }
    }

    public abstract class ConditionBase
    {
        public abstract IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId);

        public virtual int GetValue(PlayerEntity player, long[] conditionParam) => 0;

        public virtual bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (null == conditionParam) return false;
            if (conditionParam.Length == 1) return true;
            foreach (var id in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (info.BizId == id)
                {
                    return true;
                }
            }

            return false;
        }
    }

    #region condition
    public class EquipNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<PlayerEquipChange>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.GetNodeValByPath(NodePathConst.VPlayerInventoryWear, bizId);
            }

            return cnt;
        }
    }

    public class OwnNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            if (conditionId == 0)
            {
                yield break;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                yield break;
            }

            yield return EventTypeId<ItemAdd>.EventId;

            if (conditionConfig.CounterChange == CounterChange.GetValue)
            {
                yield return EventTypeId<ItemRemove>.EventId;
            }
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                var vNode = player.Root.GetNodeByPath(NodePathConst.VPlayerInventory, bizId) as IReadOnlyStackNode;
                if (vNode is StackableItemInContainer vsin)
                {
                    cnt += vsin.CountForTask;
                }

                var vSeedNode = player.Root.GetNodeByPath(NodePathConst.VSeedBackpack, bizId) as IReadOnlyStackNode;
                if (vSeedNode is StackableItemInContainer vsin1)
                {
                    cnt += vsin1.CountForTask;
                }
            }

            return cnt;
        }
    }

    public class CreateNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ManufactureItem>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticConstruction, bizId);
            }

            return cnt;
        }
    }

    public class BuildNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ConstructEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticConstruction, bizId);
            }

            return cnt;
        }
    }
    
    public class BuildNumAtOtherTerritory : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.BUILD_NUM_AT_OTHER_TERRITORY;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam) => 0;
    }

    /// <summary>
    /// 这个任务条件实现上不支持队友共享
    /// </summary>
    public class ContainerPropNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.LOOTING_STORAGE_INFO_ID;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (null == conditionParam) return false;
            var templateIds = ConditionUtil.GetConditionBizIds(conditionParam[1]);
            if (templateIds.Contains(info.BizId))
            {
                return true;
            }

            return false;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            var lootingEntity = player.ComponentLooting.CurrentLootingEntity;

            if (lootingEntity == null || lootingEntity is not ITemplateEntity templateEntity)
            {
                return cnt;
            }

            var templateIds = ConditionUtil.GetConditionBizIds(conditionParam[1]);
            if (!templateIds.Contains(templateEntity.TemplateId))
            {
                return cnt;
            }

            var lootRoot = lootingEntity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            if (lootRoot == null)
            {
                player.Logger.Warn($"[GetLootingCollectionTaskValue] lootingEntity has no RootNodeComponent {lootingEntity.EntityId}");
                return cnt;
            }

            var root = player.Root;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                foreach (var (systemId, _) in lootRoot.LootableSystemIds)
                {
                    var virtualNode = root.GetNodeByPath(systemId, PlayerInventoryNodeIndex.VirtualAll, bizId) as IReadOnlyStackNode;
                    if (virtualNode == null) continue;
                    cnt += virtualNode.Count;
                }
            }

            return cnt;
        }
    }

    public class GetAreaNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<AreaEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticArea, bizId);
            }

            return cnt;
        }
    }

    public class HandNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.HAND_ITEM_CHANGE_ID;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                var itemNode = player.Root.GetNodeById(player.CurrentWeaponId) as BaseItemNode;
                if (itemNode != null && itemNode.BizId == bizId)
                {
                    cnt += itemNode.GetCount();
                }
            }

            return cnt;
        }
    }

    public class HuntNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<HuntEvent>.EventId;
        }
    }

    public class UnlockTech : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<UnlockTechEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.ComponentBlueprint.TechnologyIsUnlock(bizId) ? 1 : 0;
            }

            return cnt;
        }
    }

    public class BuildLevelNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ConstructEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            var targetLevel = conditionParam[1];
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                var partNode = player.Root.GetNodeByPath(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Construction, bizId) as PartNode;
                if (partNode != null)
                {
                    foreach (var (level, levelCount) in partNode.LevelCount)
                    {
                        if (level >= targetLevel)
                        {
                            cnt += levelCount;
                        }
                    }
                }
            }

            return cnt;
        }
    }

    public class UseItemNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<UseItemEvent>.EventId;
        }
    }

    public class CurrencyNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ItemAdd>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (bizId == MoneyConst.PlayerReputation)
                {
                    return player.ReputationComp?.ReputationExp ?? 0;
                }
            }

            return 0;
        }
    }

    public class MonumentNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<MonumentChangeInfo>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.MonumentId == bizId ? 1 : 0;
            }

            return cnt;
        }
    }

    public class TaskCompletedNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.TASK_TYPE_COMPLETE_EVENT_ID;
        }
    }

    public class OwnNumForType : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            if (conditionId == 0)
            {
                yield break;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                yield break;
            }

            yield return ConditionInnerClassId.ITEM_MANUFACTURING_ADD_ID;
            if (conditionConfig.CounterChange == CounterChange.GetValue)
            {
                yield return ConditionInnerClassId.ITEM_MANUFACTURING_REMOVE_ID;
            }
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.GetNodeValByPath(NodePathConst.VPlayerInventoryManufacturing, bizId);
                cnt += player.Root.GetNodeValByPath(NodePathConst.VSeedBackpackManufacturing, bizId);
            }

            return cnt;
        }
    }

    public class Drive : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<DriveEvent>.EventId;
        }
    }

    public class OpenCardRoomDoor : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<OpenCardRoomEvent>.EventId;
        }
    }

    public class OpenTreasureChest : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.OPEN_TREASURE_CHEST_ID;
        }
    }

    public class KillNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.KILL_EVENT_ID;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticKill, bizId);
            }

            return cnt;
        }
    }

    public class DamageNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<DamageEvent>.EventId;
        }
    }

    public class OpenTreasureChestType : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.OPEN_TREASURE_CHEST_FOR_TYPE_ID;
        }
    }

    public class FinishEventNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<FinishActionEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (bizId == McCommon.Tables.TbQuestConst.ActivateReputation)
                {
                    var reputationComp = player.GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId);
                    if (reputationComp != null && reputationComp.ReputationCabinetId > 0 || (player.MyTeam != null && player.MyTeam.TeamReputationInfo.ReputationCabinetId > 0))
                    {
                        cnt++;
                    }
                }
            }

            return cnt;
        }
    }

    public class UnlockBlueprint : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<UnlockBlueprintEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.ComponentBlueprint.BluePrintIsUnlock(bizId) ? 1 : 0;
            }

            return cnt;
        }
    }

    public class CallIntelligenceBonus : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<CallIntelligenceBonusEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticReputation, bizId);
            }

            return cnt;
        }
    }

    public class ConsumeItem : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ItemRemove>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (!base.NeedUpdate(player, ref info, subTaskId, conditionId, conditionParam))
            {
                return false;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemComsume.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemConsumeId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemUseType(ref info, itemPathConfig.ItemConsumeId);
        }
    }

    public class ConsumeItemForType : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.ITEM_MANUFACTURING_REMOVE_ID;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (!base.NeedUpdate(player, ref info, subTaskId, conditionId, conditionParam))
            {
                return false;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            if (conditionParam.Length < 2)
            {
                return true;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemComsume.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemConsumeId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemUseType(ref info, itemPathConfig.ItemConsumeId);
        }
    }

    public class ConsumeAny : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ItemRemove>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            if (conditionParam.Length < 2)
            {
                return true;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemComsume.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemConsumeId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemUseType(ref info, itemPathConfig.ItemConsumeId);
        }
    }

    public class GetItem : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ItemAdd>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (!base.NeedUpdate(player, ref info, subTaskId, conditionId, conditionParam))
            {
                return false;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            if (conditionParam.Length < 2)
            {
                return true;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemSource.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemSourceId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemGetType(ref info, itemPathConfig.ItemSourceId);
        }
    }

    public class GetItemForType : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.ITEM_MANUFACTURING_ADD_ID;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (!base.NeedUpdate(player, ref info, subTaskId, conditionId, conditionParam))
            {
                return false;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            if (conditionParam.Length < 2)
            {
                return true;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemSource.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemSourceId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemGetType(ref info, itemPathConfig.ItemSourceId);
        }
    }

    public class GetItemAny : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ItemAdd>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(conditionId);
            if (conditionConfig == null)
            {
                return false;
            }

            if (conditionParam.Length < 2)
            {
                return true;
            }

            var itemPathConfig = McCommon.Tables.TbMissionItemSource.GetOrDefault((int)conditionParam[1]);
            if (itemPathConfig == null || itemPathConfig.ItemSourceId == null)
            {
                return true;
            }

            return ConditionUtil.CheckItemGetType(ref info, itemPathConfig.ItemSourceId);
        }
    }

    public class ClientTrigger : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.CLIENT_TRIGGER_EVENT_ID;
        }
    }

    public class UnityDsTrigger : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<UnityDsTriggerEvent>.EventId;
        }
    }

    public class DestroyPart : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<DestroyPartEvent>.EventId;
        }
    }

    public class DestroyUniquePart : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<DestroyPartEvent>.EventId;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                // 获取 Destroy/BizId 路径下的子节点数量，即为在不同领地摧毁该类型部件的唯一数量
                if (player.Root.GetNodeByPath(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Destroy,
                        bizId) is
                    DirectoryNode bizIdNode)
                {
                    cnt += bizIdNode.ChildCount;
                }
            }

            return cnt;
        }
    }

    public class CollectNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<CollectEvent>.EventId;
        }
    }

    public class TaskFinish : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<TaskFinishEvent>.EventId;
        }
    }

    public class PatrolTeamKillNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.PATROL_TEAM_KILL_ID;
        }
    }

    public class NPCTeamKillNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<PoiTeamKillEvent>.EventId;
        }
    }

    public class VehicleTeamKillNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.GUNSHIP_KILL_ID;
        }
    }

    public class VisitShop : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<VisitShopEvent>.EventId;
        }
    }

    public class StartEngine : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<StartTrainCarEvent>.EventId;
        }
    }

    public class PerfectDelivery : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.PERFECT_DELIVERY_ID;
        }
    }

    public class DisassembleItem : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.DECOMPOSER_ITEM_ID;
        }
    }

    public class DisassembleAnyItem : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.DECOMPOSER_ANY_ITEM_ID;
        }
    }

    public class OpenPlayerDeathDropBox : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<PlayerLootingBoxEvent>.EventId;
        }
    }
    
    public class CompleteMiniGame : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<CompleteMiniGameEvent>.EventId;
        }
    }

    public class OpenSpawnRuleTreasureChest : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.OPEN_TREASURE_CHEST_FOR_SPAWN_RULE_ID;
        }
    }

    public class EnterAreaBox : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<EnterAreaBoxEvent>.EventId;
        }
    }

    public class Unload : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.UNLOAD_ID;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId,
            long[] conditionParam)
        {
            if (conditionParam == null) return true; // 兼容旧配置，默认处理所有卸货

            // 检查 bizId 匹配（如果配置了的话）
            if (conditionParam.Length >= 2)
            {
                // conditionParam[1] 是目标 bizId，支持 ID 组
                bool bizIdMatched = false;
                foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[1]))
                {
                    if (bizId == -1 || info.BizId == bizId)
                    {
                        bizIdMatched = true;
                        break;
                    }
                }

                if (!bizIdMatched)
                {
                    return false; // bizId 不匹配，直接返回 false
                }
            }

            // 检查卸货方式（如果配置了的话）
            if (conditionParam.Length >= 3)
            {
                // conditionParam[2] 是期望的卸货方式
                // 0=手动, 1=权限卡, 2=高级权限卡, -1=任意方式
                int expectedUnloadType = (int)conditionParam[2];

                // -1 表示任意卸货方式都可以
                if (expectedUnloadType == -1) return true;

                // 检查事件中的卸货方式是否匹配
                int actualUnloadType = info.Count; // 我们通过Count字段传递卸货方式
                return actualUnloadType == expectedUnloadType;
            }

            return true; // 没有配置卸货方式限制，默认允许所有
        }
    }

    public class RepairItem : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<RepairItemEvent>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (null == conditionParam) return false;

            foreach (var id in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (id == -1 || info.BizId == id)
                {
                    return true;
                }
            }

            return false;
        }
    }

    public class TeamUp : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.TEAM_UP;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam) => player.MyTeam != null ? 1 : 0;
    }

    public class GrowCrops : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<GrowCropsEvent>.EventId;
        }
    }

    public class Injured : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.INJURED;
        }
    }

    public class Death : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.DEATH;
        }
    }

    public class ElectricConnection : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<ElectricConnectionEvent>.EventId;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            var orignEvent = info.OriginEvent as ElectricConnectionEvent;
            if (conditionParam.Length < 2)
            {
                return false;
            }

            bool isInputMatched = false;
            bool isOutputMatched = false;
            foreach (var param in conditionParam)
            {
                foreach (var id in ConditionUtil.GetConditionBizIds(param))
                {
                    if (id == orignEvent.BizId) isInputMatched = true;
                    if (id == orignEvent.OutputTempalateId) isOutputMatched = true;
                }
            }

            return isInputMatched && isOutputMatched;
        }
    }

    public class BaseProtectionNumLimit : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.BASE_PROTECTOIN_NUM_LIMIT;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam) => true;

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var damageableComp = player.GetComponent<DamageableComponent>(EComponentIdEnum.Damageable);
            if (null == damageableComp)
            {
                return 0;
            }

            var curProcess = 0f;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (bizId < damageableComp.BaseProtection.Length)
                {
                    curProcess = Mathf.Max(curProcess, damageableComp.BaseProtection[bizId]);
                }
            }

            return Mathf.RoundToInt(curProcess * 100);
        }
    }

    public class AreaProtectionNumLimit : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.AREA_PROTECTOIN_NUM_LIMIT;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam) => true;
        
        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var damageableComp = player.GetComponent<DamageableComponent>(EComponentIdEnum.Damageable);
            if (null == damageableComp || damageableComp.AreaProtections.Length <= 0)
            {
                return 0;
            }

            var curProcess = float.MaxValue;
            var finalProcess = 0f;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                for (int index = (int)HitPart.Head; index <= (int)HitPart.LowerBody; index++)
                {
                    var array = damageableComp.AreaProtections[index];
                    if (bizId < array.Length)
                    {
                        curProcess = Mathf.Min(curProcess, array[bizId]);
                        finalProcess = curProcess;
                    }
                }
            }

            return Mathf.RoundToInt(finalProcess * 100);
        }
    }

    public class CallMonumentElevator : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.CALL_MONUMENT_LELEVATOR;
        }
    }
    
    public class Gesture : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<GestureEvent>.EventId;
        }
    }

    public class TeamInvite : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.TEAM_INVITE;
        }
    }

    public class PossessingTechnology : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.POSSESSING_TECHNOLOGY;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam) => true;

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            if (conditionParam.Length != 2) return 0;

            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                cnt += player.ComponentBlueprint.IsShareTechnologyUnlock(bizId) ? 1 : 0;
            }

            return cnt;
        }
    }

    public class EquipWeapon : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.EQUIP_WEAPON;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam) => true;

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            if (conditionParam.Length != 3) return 0;

            var param = ConditionUtil.GetConditionBizIds(conditionParam[1]).ToList();
            if (param == null || param.Count == 0) return 0;
            
            bool isAllMatch = conditionParam[2] == 1;
            long weaponBizId = param[0];
            param.RemoveAt(0);

            var curProcess = 0;
            foreach (var weaponNode in GetPlayerWeaponItemNode(player))
            {
                if (weaponBizId == weaponNode.BizId)
                {
                    if (param.Count != 0)
                    {
                        if (IsPartsMatch(param, weaponNode, isAllMatch))
                        {
                            curProcess++;
                        }
                    }
                    else
                    {
                        curProcess++;
                    }
                }
            }

            return curProcess;
        }

        private IEnumerable<WeaponItemNode> GetPlayerWeaponItemNode(PlayerEntity player)
        {
            var container = player.Root.GetNodeByPath(NodePathConst.PlayerInventoryBelt) as ItemContainerNode;
            foreach (var (id, node) in container)
            {
                if (node is WeaponItemNode weaponNode)
                {
                    yield return weaponNode;
                }
            }
        }

        private IEnumerable<long> GetWeaponParts(WeaponItemNode weaponNode)
        {
            foreach (var (id, node) in weaponNode)
            {
                if (node is WeaponAccessoryItemNode weaponAccessoryNode)
                {
                    yield return weaponAccessoryNode.BizId;
                }
            }
        }

        private bool IsPartsMatch(List<long> parts, WeaponItemNode weaponNode, bool isAllMatch)
        {
            int matchCount = 0;
            foreach (var partId in GetWeaponParts(weaponNode))
            {
                if (parts.Contains(partId))
                {
                    matchCount++;
                }
            }

            if (isAllMatch)
            {
                return matchCount == parts.Count;
            }
            else
            {
                return matchCount != 0;
            }
        }
    }

    public class CompleteGame : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.COMPLETE_GAME;
        }
    }

    public class WinGame : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.WIN_GAME;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (conditionParam.Length < 3)
            {
                return true;
            }

            var orginEvt = info.OriginEvent as WinGameInfo;
            var expectCampId = conditionParam[1];
            if (expectCampId != orginEvt.CampId)
            {
                return false;
            }

            foreach (var id in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (info.BizId == id)
                {
                    return true;
                }
            }

            return false;
        }
    }

    public class ActiveSafetyBox : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.ACTIVE_SAFETY_BOX;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam) => player.SafetyBoxEntityId != 0 ? 1 : 0;
    }
    
    public class RepairDebris : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<RepairDebrisEvent>.EventId;
        }
    }

    public class ComfortNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.COMFORT_NUM;
        }
        
        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            return Mathf.CeilToInt(player.Comfort * 100);
        }
    }
    
    public class HydrationNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.HYDRATION_NUM;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            return player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticPropertyIncrement, PlayerEntity.PropertyIds.HYDRATION);
        }
    }

    public class CaloriesNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.CALORIES_NUM;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            return player.Root.TryGetNodeValByPath(NodePathConst.DataStatisticPropertyIncrement, PlayerEntity.PropertyIds.CALORIES);
        }
    }
    
    public class OccupyTerritory : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<OccupyTerritoryEvent>.EventId;
        }
    }

    public class InfoConvertedNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<FinishConvertReputationEvent>.EventId;
        }
    }

    public class KillUniqueNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.KILL_EVENT_ID;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            var cnt = 0;
            foreach (var selectId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                // 获取 Kill/DamageConditionId 路径下的子节点数量，即为 UniqueKill 数量
                if (player.Root.GetNodeByPath(NodeSystemType.DataStatisticSystem, DataStatisticContainerNodeIndex.Kill, selectId) is
                    DirectoryNode damageConditionNode)
                {
                    cnt += damageConditionNode.ChildCount;
                }
            }

            return cnt;
        }
    }
    
    public class TakeVehicle : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.TAKE_VEHICLE;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            if (player.MountableId <= 0) return 0;
            
            long templateId = -1;
            var vehicleEntity = EntityManager.Instance.GetEntity(player.MountableId) as ITemplateEntity;
            if (vehicleEntity == null)
            {
                var embeddedCustom = EmbeddedCustomManager.Instance.GetEmbedded(player.MountableId);
                if (null == embeddedCustom) return 0;

                var parentEntity = embeddedCustom.ParentRef as ITemplateEntity;
                if (null == parentEntity) return 0;

                templateId = parentEntity.TemplateId;
            }
            else
            {
                templateId = vehicleEntity.TemplateId;
            }

            if (templateId == -1) return 0;

            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (bizId == templateId)
                {
                    cnt++;
                }
            }

            return cnt;
        }


    }

    public class DestroyTerritoryCabinet : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.DESTROY_TERRITORYCABINET;
        }
    }

    public class SleepingBagNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.SLEEPING_BAG;
        }

        public override int GetValue(PlayerEntity player, long[] conditionParam)
        {
            if (conditionParam.Length == 1 || conditionParam[^1] == -1)
            {
                return player.ConstructionComp.SleepingBagInfo.Count;
            }

            var cnt = 0;
            foreach (var bizId in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                foreach (var (_, sleepingBagInfo) in player.ConstructionComp.SleepingBagInfo)
                {
                    if (sleepingBagInfo.TemplateId == bizId)
                    {
                        cnt++;
                    }
                }
            }

            return cnt;
        }
    }
    
    public class PurchaseGoods : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return EventTypeId<PurchaseGoodsEvent>.EventId;
        }
    }
        
    public class PlacePropsIntoContainer : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.PLACE_PROPS_INTO_CONTAINER;
        }

        public override bool NeedUpdate(PlayerEntity player, ref ConditionInnerInfo info, long subTaskId, int conditionId, long[] conditionParam)
        {
            if (conditionParam.Length < 3)
            {
                return true;
            }

            var orginEvt = info.OriginEvent as PlacePropsIntoContainerInfo;
            var expectTemplateId = conditionParam[1];
            if (expectTemplateId != orginEvt.TemplateId)
            {
                return false;
            }

            foreach (var id in ConditionUtil.GetConditionBizIds(conditionParam[^1]))
            {
                if (info.BizId == id)
                {
                    return true;
                }
            }

            return false;
        }
    }

    public class SaveBlueprintNum : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.SAVE_BLUEPRINT;
        }
    }

    public class ConstructUseBlueprint : ConditionBase
    {
        public override IEnumerable<int> GetInterestEventId(PlayerEntity player, int conditionId)
        {
            yield return ConditionInnerClassId.CONSTRUCT_USE_BLUEPRINT;
        }
    }
    #endregion
}
