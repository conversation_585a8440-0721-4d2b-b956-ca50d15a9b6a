{"m_DefineSymbols": {"m_Value": {"Android": "ODIN_INSPECTOR;ODIN_INSPECTOR_3;AMPLIFY_SHADER_EDITOR;SOC_CLIENT;ENABLE_IL2CPP;STARTER_ASSETS_PACKAGES_CHECKED;_SOC_CLIENT;NWH_NVP2;NWH_WC3D;SOC_ANIM_GRAPH;ENABL<PERSON>_GPU_DRIVEN;ODIN_INSPECTOR_3_1;MAGI<PERSON>CLOTH2;SOC_NEWTP;SHADER_BUILD_INFO;GPU_DRIVEN_STREAMING;ENABLE_STRIPPING_CODE;USE_HYBRIDCLR_8_1_0;PMS_URP;PMS_CINEMACHINE;PMS_CINE_MACHINE_2_6_OR_NEWER;PMS_CINE_MACHINE_2_6_1_OR_NEWER;PMS_CINE_MACHINE_2_8_OR_NEWER", "Standalone": "ODIN_INSPECTOR;ODIN_INSPECTOR_3;AMPLIFY_SHADER_EDITOR;SOC_CLIENT;ENABLE_IL2CPP;STARTER_ASSETS_PACKAGES_CHECKED;_SOC_CLIENT;NWH_WC3D;NWH_NVP2;ODIN_INSPECTOR_3_1;SOC_ANIM_GRAPH;MAGICACLOTH2;SOC_NEWTP;SHADER_BUILD_INFO;ENABLE_STRIPPING_CODE;GCLOUD_MSDK_WINDOWS;USE_HYBRIDCLR_8_1_0;ENABLE_GPU_DRIVEN;GPU_DRIVEN_STREAMING;PMS_URP;PMS_CINEMACHINE;PMS_CINE_MACHINE_2_6_OR_NEWER;PMS_CINE_MACHINE_2_6_1_OR_NEWER;PMS_CINE_MACHINE_2_8_OR_NEWER", "iPhone": "AMPLIFY_SHADER_EDITOR;ODIN_INSPECTOR;ODIN_INSPECTOR_3;NWH_NVP2;NWH_WC3D;SOC_ANIM_GRAPH;SOC_NEWTP;SHADER_BUILD_INFO;ODIN_INSPECTOR_3_1;MAGICACLOTH2;ENABLE_STRIPPING_CODE;SHADER_VARIANT_CONTROL;GPU_DRIVEN_STREAMING;USE_HYBRIDCLR_8_1_0"}, "m_Initialized": true}, "m_AllowUnsafeCode": {"m_Value": false, "m_Initialized": false}, "m_ScriptDebugInfoEnabled": {"m_Value": true, "m_Initialized": true}}