using System;
using System.Reflection;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.SocWorld.Helpers;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocWorld.Component
{
    [HotfixClass]
    public static partial class FunctionSwitchComponentHotfix
    {
        [Hotfix]
        private static void Init(this FunctionSwitchComponent self)
        {
            self.Root = new(NodeSystemType.Invaild);

            // 没想到更好的办法，先反射做
            var tbl = McCommon.Tables.TbConfig.GetOrDefault(PlayHelper.GetPlayId());
            if (tbl == null)
            {
                throw new Exception($"FunctionSwitchComponent.Init failed, playId:{PlayHelper.GetPlayId()} not found in TbConfig");
            }
            Type type = typeof(FunctionConst);

            // 获取所有静态字段（包括public和非public）
            FieldInfo[] fields = type.GetFields(
                BindingFlags.Public |
                BindingFlags.Static);

            Type targetType = typeof(FunctionName);

            foreach (FieldInfo field in fields)
            {
                if (targetType.IsAssignableFrom(field.FieldType))
                {
                    var function = field.GetValue(null) as FunctionName;
                    var node = self.Root.EnsureNode<DirectoryWithBoolNode, DirectoryWithBoolNode>(function.Path);

                    if (string.IsNullOrEmpty(function.TableColumnName))
                    {
                        node.Value = true; // 如果没有指定表字段名，说明不是策划配置，或者由其它表项设置，默认功能开启
                    }
                    else
                    {
                        if (ReflectionHelper.TryGetProperty<bool>(tbl, function.TableColumnName, out var isOpen))
                        {
                            node.Value = isOpen;
                        }
                        else
                        {
                            // 如果没有找到字段，默认设置为false
                            throw new Exception($"FunctionSwitchComponent.Init failed, TbConfig does not contain field {function.TableColumnName}");
                        }
                    }
                }
            }

            // 这个表项大厅也要读，所以不在TbConfig表里
            var medalTaskSwitch = self.Root.EnsureNode<DirectoryWithBoolNode, DirectoryWithBoolNode>(FunctionConst.MedalTask.Path);
            medalTaskSwitch.Value = McCommon.Tables.TBGameMode.GetOrDefault(ServerInstanceEntity.Instance.GameModeId)?.IsRank ?? false;
        }

        [Hotfix]
        private static void PostInit(this FunctionSwitchComponent self, bool isLoadFromDb)
        {
            FunctionSwitchComponent.Instance = self;
        }

        public static void SetEnable(this FunctionSwitchComponent self, FunctionName configName, bool isEnable)
        {
            if (!configName.DynamicSwitch)
            {
                throw new ArgumentException($"Switch {configName.TableColumnName} does not support dynamic switch");
            }
            NodeBase cur = self.Root;
            for (var i = 0; i < configName.Path.Count; i++)
            {
                var configNode = cur.GetChildNodeAs<DirectoryWithBoolNode>(configName.Path[i]);
                if (configNode == null)
                {
                    self.Logger.Error($"Trying to set config of {configName.TableColumnName}, path {NodeHelper.PrintPath(configName.Path)}, but node not found");
                    return;
                }
                if (i == configName.Path.Count - 1)
                {
                    configNode.Value = isEnable;
                    return;
                }
                cur = configNode;
            }
        }
    }
}
