using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            AddEntityOnlyInitComponents(player);
            ServerConfig.Instance.ClosePushToLobby = 1;
            InitMedalTaskContainer();
        }

        private void InitMedalTaskContainer()
        {
            FunctionConst.Task.EnableDynamicSwitch();
            FunctionSwitchComponent.Instance.SetEnable(FunctionConst.Task, false);
            player.AddComponent(new PlayerTaskComponent());
            var medalTaskContainer = new MedalTaskContainer();
            player.ComponentTask.AddTaskContainer(medalTaskContainer);
            medalTaskContainer.PostInit(false);
        }

        private static void SetupMedalConfig(long medalId, long medalLevel, long styleId, long taskId, bool shouldPreCount)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + medalLevel + @",
      ""type"": 3,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + taskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          10
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": " + shouldPreCount.ToString().ToLower() + @"
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
            SetupMedalTaskConfig(taskId, 233, [5000, 26010004], 10);
        }

        private static void SetupMedalTaskConfig(long taskId, int endCondition, long[] endConditionParameter = null, int taskType = 10, int endConditionMode = 0, int isSubTask = 0, int[] subTasks = null)
        {
            var taskJson = @"{
      ""id"": " + taskId + @",
      ""type"": " + taskType + @",
      ""taskId"": " + (taskId > int.MaxValue ? 0 : (int)taskId) + @",
      ""isSubTask"": " + isSubTask + @",
      ""taskPhaseEndCondition"": " + endCondition + @",";

            // Only add endConditionParameter if it has values
            if (endConditionParameter != null && endConditionParameter.Length > 0)
                taskJson += @"""endConditionParameter"": [" + string.Join(",", endConditionParameter) + @"],";
            else
                taskJson += @"""endConditionParameter"": [],";

            taskJson += @"""endConditionMode"": " + endConditionMode + @",";

            // Only add subTasks if it has values
            if (subTasks != null && subTasks.Length > 0)
                taskJson += @"""subTasks"": [" + string.Join(",", subTasks) + @"]";
            else
                taskJson += @"""subTasks"": []";

            taskJson += @"}";

            McCommon.Tables.TbQuestPhase.Update(SimpleJSON.JSONNode.Parse(taskJson));
        }

        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}